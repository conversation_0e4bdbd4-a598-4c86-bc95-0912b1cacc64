import { test, expect } from "@playwright/test";

test.describe("Authentication Tests", () => {
  test.beforeEach(async ({ page, context }) => {
    // Clear all cookies and storage to ensure clean state
    await context.clearCookies();
    await context.clearPermissions();
    await page.goto("/");
    await page.waitForLoadState("networkidle");
  });

  test("should display authentication links in header", async ({ page }) => {
    await expect(page.locator('a[href="/auth/signin"]')).toContainText(
      "Đăng nhập"
    );
    await expect(page.locator('a[href="/auth/signup"]')).toContainText(
      "Đăng ký"
    );
  });

  test("should navigate to signin page", async ({ page }) => {
    await page.locator('a[href="/auth/signin"]').click();
    await page.waitForLoadState("networkidle");
    await expect(page).toHaveURL(/.*\/auth\/signin/);
  });

  test("should navigate to signup page", async ({ page }) => {
    await page.locator('a[href="/auth/signup"]').click();
    await page.waitForLoadState("networkidle");
    await expect(page).toHaveURL(/.*\/auth\/signup/);
  });

  test("should test signin page functionality", async ({ page }) => {
    await page.goto("/auth/signin");
    await page.waitForLoadState("networkidle");
    await page.waitForTimeout(2000);

    // Look for signin form elements
    const emailInput = page.locator(
      'input[type="email"], input[name="email"], input[placeholder*="email"]'
    );
    const passwordInput = page.locator(
      'input[type="password"], input[name="password"], input[placeholder*="password"]'
    );
    const signinButton = page.locator(
      'button:has-text("Đăng nhập"), button[type="submit"]'
    );

    if (await emailInput.isVisible().catch(() => false)) {
      await emailInput.fill("<EMAIL>");
      await expect(emailInput).toHaveValue("<EMAIL>");
      console.log("Email input tested");
    } else {
      console.log("Email input not found");
    }

    if (await passwordInput.isVisible().catch(() => false)) {
      await passwordInput.fill("testpassword");
      await expect(passwordInput).toHaveValue("testpassword");
      console.log("Password input tested");
    } else {
      console.log("Password input not found");
    }

    if (await signinButton.isVisible().catch(() => false)) {
      // Don't actually submit to avoid creating test accounts
      console.log("Signin button found and ready to submit");
    } else {
      console.log("Signin button not found");
    }

    // Look for "Forgot password" link
    const forgotPasswordLink = page.locator(
      'a:has-text("Quên mật khẩu"), a:has-text("Forgot password")'
    );
    if (await forgotPasswordLink.isVisible().catch(() => false)) {
      console.log("Forgot password link found");
    }

    // Look for "Sign up" link
    const signupLink = page.locator(
      'a:has-text("Đăng ký"), a:has-text("Sign up")'
    );
    if (await signupLink.isVisible().catch(() => false)) {
      console.log("Sign up link found");
    }
  });

  test("should test signup page functionality", async ({ page }) => {
    await page.goto("/auth/signup");
    await page.waitForLoadState("networkidle");
    await page.waitForTimeout(2000);

    // Look for signup form elements
    const nameInput = page.locator(
      'input[name="name"], input[name="fullName"], input[placeholder*="tên"], input[placeholder*="name"]'
    );
    const emailInput = page.locator(
      'input[type="email"], input[name="email"], input[placeholder*="email"]'
    );
    const passwordInput = page
      .locator(
        'input[type="password"], input[name="password"], input[placeholder*="password"]'
      )
      .first();
    const confirmPasswordInput = page.locator(
      'input[name="confirmPassword"], input[name="password_confirmation"]'
    );
    const signupButton = page.locator(
      'button:has-text("Đăng ký"), button[type="submit"]'
    );

    if (await nameInput.isVisible().catch(() => false)) {
      await nameInput.fill("Test User");
      await expect(nameInput).toHaveValue("Test User");
      console.log("Name input tested");
    } else {
      console.log("Name input not found");
    }

    if (await emailInput.isVisible().catch(() => false)) {
      await emailInput.fill("<EMAIL>");
      await expect(emailInput).toHaveValue("<EMAIL>");
      console.log("Email input tested");
    } else {
      console.log("Email input not found");
    }

    if (await passwordInput.isVisible().catch(() => false)) {
      await passwordInput.fill("testpassword");
      await expect(passwordInput).toHaveValue("testpassword");
      console.log("Password input tested");
    } else {
      console.log("Password input not found");
    }

    if (await confirmPasswordInput.isVisible().catch(() => false)) {
      await confirmPasswordInput.fill("testpassword");
      await expect(confirmPasswordInput).toHaveValue("testpassword");
      console.log("Confirm password input tested");
    } else {
      console.log("Confirm password input not found");
    }

    if (await signupButton.isVisible().catch(() => false)) {
      // Don't actually submit to avoid creating test accounts
      console.log("Signup button found and ready to submit");
    } else {
      console.log("Signup button not found");
    }

    // Look for terms and conditions checkbox
    const termsCheckbox = page.locator('input[type="checkbox"]');
    if (await termsCheckbox.isVisible().catch(() => false)) {
      await termsCheckbox.check();
      await expect(termsCheckbox).toBeChecked();
      console.log("Terms checkbox tested");
    }

    // Look for "Sign in" link
    const signinLink = page.locator(
      'a:has-text("Đăng nhập"), a:has-text("Sign in")'
    );
    if (await signinLink.isVisible().catch(() => false)) {
      console.log("Sign in link found");
    }
  });

  test("should test social login buttons if present", async ({ page }) => {
    await page.goto("/auth/signin");
    await page.waitForLoadState("networkidle");
    await page.waitForTimeout(2000);

    // Look for social login buttons
    const socialButtons = [
      page.locator('button:has-text("Google")'),
      page.locator('button:has-text("Facebook")'),
      page.locator('button:has-text("GitHub")'),
      page.locator('a:has-text("Google")'),
      page.locator('a:has-text("Facebook")'),
      page.locator('a:has-text("GitHub")'),
    ];

    let hasSocialLogin = false;
    for (const buttonGroup of socialButtons) {
      const count = await buttonGroup.count();
      if (count > 0) {
        hasSocialLogin = true;
        console.log(`Found ${count} social login buttons`);
        break;
      }
    }

    if (!hasSocialLogin) {
      console.log("No social login buttons found");
    }
  });

  test("should test form validation", async ({ page }) => {
    await page.goto("/auth/signin");
    await page.waitForLoadState("networkidle");
    await page.waitForTimeout(2000);

    // Try to submit empty form
    const signinButton = page.locator(
      'button:has-text("Đăng nhập"), button[type="submit"]'
    );
    if (await signinButton.isVisible().catch(() => false)) {
      await signinButton.click();
      await page.waitForTimeout(1000);

      // Look for validation messages
      const validationMessages = [
        page.locator("text=Email is required"),
        page.locator("text=Password is required"),
        page.locator("text=Vui lòng nhập email"),
        page.locator("text=Vui lòng nhập mật khẩu"),
        page.locator(".error-message"),
        page.locator('[role="alert"]'),
      ];

      let hasValidation = false;
      for (const messageGroup of validationMessages) {
        if (await messageGroup.isVisible().catch(() => false)) {
          hasValidation = true;
          console.log("Form validation messages found");
          break;
        }
      }

      if (!hasValidation) {
        console.log("No validation messages found");
      }
    }
  });

  test("should test invalid email format validation", async ({ page }) => {
    await page.goto("/auth/signin");
    await page.waitForLoadState("networkidle");
    await page.waitForTimeout(2000);

    const emailInput = page.locator(
      'input[type="email"], input[name="email"], input[placeholder*="email"]'
    );
    const signinButton = page.locator(
      'button:has-text("Đăng nhập"), button[type="submit"]'
    );

    if (
      (await emailInput.isVisible().catch(() => false)) &&
      (await signinButton.isVisible().catch(() => false))
    ) {
      // Enter invalid email
      await emailInput.fill("invalid-email");
      await signinButton.click();
      await page.waitForTimeout(1000);

      // Look for email validation message
      const emailValidationMessages = [
        page.locator("text=Invalid email"),
        page.locator("text=Email không hợp lệ"),
        page.locator("text=Please enter a valid email"),
        page.locator("text=Vui lòng nhập email hợp lệ"),
      ];

      let hasEmailValidation = false;
      for (const message of emailValidationMessages) {
        if (await message.isVisible().catch(() => false)) {
          hasEmailValidation = true;
          console.log("Email format validation found");
          break;
        }
      }

      if (!hasEmailValidation) {
        console.log("No email format validation found");
      }
    }
  });

  test("should test password strength requirements", async ({ page }) => {
    await page.goto("/auth/signup");
    await page.waitForLoadState("networkidle");
    await page.waitForTimeout(2000);

    const passwordInput = page
      .locator(
        'input[type="password"], input[name="password"], input[placeholder*="password"]'
      )
      .first();

    if (await passwordInput.isVisible().catch(() => false)) {
      // Test weak password
      await passwordInput.fill("123");
      await page.waitForTimeout(1000);

      // Look for password strength indicators
      const passwordStrengthIndicators = [
        page.locator("text=Weak"),
        page.locator("text=Yếu"),
        page.locator("text=Password too short"),
        page.locator("text=Mật khẩu quá ngắn"),
        page.locator(".password-strength"),
        page.locator('[data-testid*="password-strength"]'),
      ];

      let hasPasswordStrength = false;
      for (const indicator of passwordStrengthIndicators) {
        if (await indicator.isVisible().catch(() => false)) {
          hasPasswordStrength = true;
          console.log("Password strength indicator found");
          break;
        }
      }

      if (!hasPasswordStrength) {
        console.log("No password strength indicators found");
      }
    }
  });

  test("should test remember me functionality", async ({ page }) => {
    await page.goto("/auth/signin");
    await page.waitForLoadState("networkidle");
    await page.waitForTimeout(2000);

    // Look for remember me checkbox
    const rememberMeCheckbox = page.locator(
      'input[type="checkbox"][name*="remember"], input[type="checkbox"]:near(text="Remember me"), input[type="checkbox"]:near(text="Ghi nhớ")'
    );

    if (await rememberMeCheckbox.isVisible().catch(() => false)) {
      await rememberMeCheckbox.check();
      await expect(rememberMeCheckbox).toBeChecked();
      console.log("Remember me checkbox tested");
    } else {
      console.log("Remember me checkbox not found");
    }
  });

  test("should test logout functionality if user is logged in", async ({
    page,
  }) => {
    // This test would only work if there's a way to be logged in
    // For now, just check if logout button exists
    const logoutButton = page.locator(
      'button:has-text("Đăng xuất"), button:has-text("Logout"), a:has-text("Đăng xuất"), a:has-text("Logout")'
    );

    if (await logoutButton.isVisible().catch(() => false)) {
      console.log("Logout button found");
      // Don't actually click to avoid logging out
    } else {
      console.log("Logout button not found (user likely not logged in)");
    }
  });
});
