import { test, expect } from '@playwright/test';

test.describe('Products Page Tests', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/products');
  });

  test('should load products page successfully', async ({ page }) => {
    await expect(page).toHaveTitle(/NS Shop/);
    // Wait for products to load
    await page.waitForLoadState('networkidle');
  });

  test('should display page header and navigation', async ({ page }) => {
    // Check if we're on products page
    await expect(page).toHaveURL('/products');
    
    // Check navigation is still present
    const nav = page.locator('nav');
    await expect(nav.locator('a[href="/products"]')).toBeVisible();
  });

  test('should display search functionality', async ({ page }) => {
    const searchInput = page.locator('input[type="search"], input[placeholder*="tìm kiếm"], input[placeholder*="search"]').first();
    
    if (await searchInput.isVisible()) {
      await searchInput.fill('áo thun');
      await expect(searchInput).toHaveValue('áo thun');
      
      // Test search submission
      await searchInput.press('Enter');
      await page.waitForLoadState('networkidle');
    }
  });

  test('should display filter options', async ({ page }) => {
    // Look for common filter elements
    const filterElements = [
      page.locator('text=Danh mục'),
      page.locator('text=Giá'),
      page.locator('text=Sắp xếp'),
      page.locator('select'),
      page.locator('input[type="range"]'),
      page.locator('button:has-text("Lọc")'),
      page.locator('button:has-text("Filter")')
    ];

    // Check if any filter elements are visible
    let hasFilters = false;
    for (const element of filterElements) {
      if (await element.first().isVisible().catch(() => false)) {
        hasFilters = true;
        break;
      }
    }

    // If no filters found, that's also valid - just log it
    if (!hasFilters) {
      console.log('No filter elements found on products page');
    }
  });

  test('should display sorting options', async ({ page }) => {
    // Look for sorting dropdown or buttons
    const sortElements = [
      page.locator('select:has(option)'),
      page.locator('button:has-text("Sắp xếp")'),
      page.locator('button:has-text("Sort")'),
      page.locator('[data-testid*="sort"]'),
      page.locator('text=Giá thấp đến cao'),
      page.locator('text=Giá cao đến thấp'),
      page.locator('text=Mới nhất')
    ];

    let hasSorting = false;
    for (const element of sortElements) {
      if (await element.first().isVisible().catch(() => false)) {
        hasSorting = true;
        break;
      }
    }

    if (!hasSorting) {
      console.log('No sorting elements found on products page');
    }
  });

  test('should display product grid/list', async ({ page }) => {
    // Wait for content to load
    await page.waitForTimeout(2000);
    
    // Look for product cards or list items
    const productElements = [
      page.locator('[data-testid*="product"]'),
      page.locator('.product-card'),
      page.locator('.product-item'),
      page.locator('a[href*="/products/"]'),
      page.locator('img[alt*="product"], img[alt*="sản phẩm"]')
    ];

    let hasProducts = false;
    for (const element of productElements) {
      const count = await element.count();
      if (count > 0) {
        hasProducts = true;
        console.log(`Found ${count} product elements`);
        break;
      }
    }

    if (!hasProducts) {
      // Check if there's a "no products" message
      const noProductsMessages = [
        page.locator('text=Không có sản phẩm'),
        page.locator('text=No products'),
        page.locator('text=Chưa có sản phẩm')
      ];

      let hasNoProductsMessage = false;
      for (const message of noProductsMessages) {
        if (await message.isVisible().catch(() => false)) {
          hasNoProductsMessage = true;
          break;
        }
      }

      if (!hasNoProductsMessage) {
        console.log('No products or no-products message found');
      }
    }
  });

  test('should display view toggle buttons', async ({ page }) => {
    // Look for grid/list view toggle buttons
    const viewToggleElements = [
      page.locator('button[aria-label*="grid"]'),
      page.locator('button[aria-label*="list"]'),
      page.locator('button:has-text("Grid")'),
      page.locator('button:has-text("List")'),
      page.locator('[data-testid*="view"]')
    ];

    let hasViewToggle = false;
    for (const element of viewToggleElements) {
      if (await element.first().isVisible().catch(() => false)) {
        hasViewToggle = true;
        break;
      }
    }

    if (!hasViewToggle) {
      console.log('No view toggle buttons found');
    }
  });

  test('should test pagination if present', async ({ page }) => {
    await page.waitForTimeout(2000);
    
    // Look for pagination elements
    const paginationElements = [
      page.locator('nav[aria-label*="pagination"]'),
      page.locator('.pagination'),
      page.locator('button:has-text("Next")'),
      page.locator('button:has-text("Tiếp")'),
      page.locator('button:has-text("Previous")'),
      page.locator('button:has-text("Trước")'),
      page.locator('a:has-text("1")'),
      page.locator('a:has-text("2")')
    ];

    let hasPagination = false;
    for (const element of paginationElements) {
      if (await element.first().isVisible().catch(() => false)) {
        hasPagination = true;
        console.log('Pagination found');
        break;
      }
    }

    if (!hasPagination) {
      console.log('No pagination found');
    }
  });

  test('should test product card interactions', async ({ page }) => {
    await page.waitForTimeout(2000);
    
    // Find product links
    const productLinks = page.locator('a[href*="/products/"]');
    const count = await productLinks.count();
    
    if (count > 0) {
      // Test clicking on first product
      const firstProduct = productLinks.first();
      const href = await firstProduct.getAttribute('href');
      
      if (href) {
        await firstProduct.click();
        await expect(page).toHaveURL(new RegExp(href));
        
        // Go back to products page
        await page.goBack();
        await expect(page).toHaveURL('/products');
      }
    } else {
      console.log('No product links found to test');
    }
  });

  test('should test add to cart functionality', async ({ page }) => {
    await page.waitForTimeout(2000);
    
    // Look for add to cart buttons
    const addToCartButtons = [
      page.locator('button:has-text("Thêm vào giỏ")'),
      page.locator('button:has-text("Add to cart")'),
      page.locator('button[data-testid*="add-to-cart"]'),
      page.locator('.add-to-cart')
    ];

    let hasAddToCart = false;
    for (const buttonGroup of addToCartButtons) {
      const count = await buttonGroup.count();
      if (count > 0) {
        hasAddToCart = true;
        
        // Test clicking first add to cart button
        const firstButton = buttonGroup.first();
        await firstButton.click();
        
        // Wait for any response
        await page.waitForTimeout(1000);
        
        // Check if cart count updated (this would depend on implementation)
        const cartCount = page.locator('button:has-text("1"), .cart-count:has-text("1")');
        if (await cartCount.isVisible().catch(() => false)) {
          console.log('Cart count updated successfully');
        }
        
        break;
      }
    }

    if (!hasAddToCart) {
      console.log('No add to cart buttons found');
    }
  });

  test('should test wishlist functionality if present', async ({ page }) => {
    await page.waitForTimeout(2000);
    
    // Look for wishlist/heart buttons
    const wishlistButtons = [
      page.locator('button[aria-label*="wishlist"]'),
      page.locator('button[aria-label*="favorite"]'),
      page.locator('button:has(svg[data-lucide="heart"])'),
      page.locator('.wishlist-btn'),
      page.locator('[data-testid*="wishlist"]')
    ];

    let hasWishlist = false;
    for (const buttonGroup of wishlistButtons) {
      const count = await buttonGroup.count();
      if (count > 0) {
        hasWishlist = true;
        
        // Test clicking first wishlist button
        const firstButton = buttonGroup.first();
        await firstButton.click();
        await page.waitForTimeout(1000);
        
        console.log('Wishlist button clicked');
        break;
      }
    }

    if (!hasWishlist) {
      console.log('No wishlist buttons found');
    }
  });

  test('should test price range filter if present', async ({ page }) => {
    await page.waitForTimeout(2000);
    
    // Look for price range inputs
    const priceInputs = [
      page.locator('input[type="range"]'),
      page.locator('input[placeholder*="Giá"]'),
      page.locator('input[placeholder*="Price"]'),
      page.locator('input[name*="price"]'),
      page.locator('.price-range input')
    ];

    let hasPriceFilter = false;
    for (const inputGroup of priceInputs) {
      const count = await inputGroup.count();
      if (count > 0) {
        hasPriceFilter = true;
        
        // Test price range interaction
        const firstInput = inputGroup.first();
        const inputType = await firstInput.getAttribute('type');
        
        if (inputType === 'range') {
          await firstInput.fill('50');
        } else {
          await firstInput.fill('100000');
        }
        
        await page.waitForTimeout(1000);
        console.log('Price filter tested');
        break;
      }
    }

    if (!hasPriceFilter) {
      console.log('No price filter found');
    }
  });

  test('should test category filter if present', async ({ page }) => {
    await page.waitForTimeout(2000);
    
    // Look for category filters
    const categoryFilters = [
      page.locator('select[name*="category"]'),
      page.locator('input[type="checkbox"][name*="category"]'),
      page.locator('button:has-text("Áo thun")'),
      page.locator('button:has-text("Váy đầm")'),
      page.locator('.category-filter')
    ];

    let hasCategoryFilter = false;
    for (const filterGroup of categoryFilters) {
      const count = await filterGroup.count();
      if (count > 0) {
        hasCategoryFilter = true;
        
        // Test category filter interaction
        const firstFilter = filterGroup.first();
        const tagName = await firstFilter.evaluate(el => el.tagName.toLowerCase());
        
        if (tagName === 'select') {
          await firstFilter.selectOption({ index: 1 });
        } else if (tagName === 'input') {
          await firstFilter.check();
        } else {
          await firstFilter.click();
        }
        
        await page.waitForTimeout(1000);
        console.log('Category filter tested');
        break;
      }
    }

    if (!hasCategoryFilter) {
      console.log('No category filter found');
    }
  });
});
