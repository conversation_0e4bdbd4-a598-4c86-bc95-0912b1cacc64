import { test, expect } from "@playwright/test";

test.describe("Homepage Tests", () => {
  test.beforeEach(async ({ page }) => {
    await page.goto("/");
  });

  test("should load homepage successfully", async ({ page }) => {
    await expect(page).toHaveTitle(/NS Shop/);
    await expect(page.locator("h1")).toContainText("Khám phá");
    await expect(page.locator("h1")).toContainText("Phong cách");
    await expect(page.locator("h1")).toContainText("Của bạn");
  });

  test("should display navigation menu correctly", async ({ page }) => {
    const nav = page.locator("nav");
    await expect(nav.locator('a[href="/"]')).toContainText("Trang chủ");
    await expect(nav.locator('a[href="/products"]')).toContainText("Sản phẩm");
    await expect(nav.locator('a[href="/categories"]')).toContainText(
      "Danh mục"
    );
    await expect(nav.locator('a[href="/about"]')).toContainText("Về chúng tôi");
    await expect(nav.locator('a[href="/contact"]')).toContainText("Liên hệ");
  });

  test("should display logo and be clickable", async ({ page }) => {
    const logo = page.locator('a[href="/"]').first();
    await expect(logo).toContainText("NS Shop");
    await logo.click();
    await expect(page).toHaveURL("/");
  });

  test("should display search functionality", async ({ page }) => {
    const searchBox = page.locator('input[placeholder*="Tìm kiếm"]');
    await expect(searchBox).toBeVisible();

    // Test search input
    await searchBox.fill("áo thun");
    await expect(searchBox).toHaveValue("áo thun");
  });

  test("should display cart with initial count", async ({ page }) => {
    const cartButton = page.locator('button:has-text("0")');
    await expect(cartButton).toBeVisible();
    await expect(cartButton).toContainText("0");
  });

  test("should display authentication links", async ({ page }) => {
    await expect(page.locator('a[href="/auth/signin"]')).toContainText(
      "Đăng nhập"
    );
    await expect(page.locator('a[href="/auth/signup"]')).toContainText(
      "Đăng ký"
    );
  });

  test("should display hero section with content", async ({ page }) => {
    const heroSection = page.locator("main").first();
    await expect(heroSection.locator("h1")).toContainText("Khám phá");
    await expect(heroSection.locator("h1")).toContainText("Phong cách");
    await expect(heroSection.locator("h1")).toContainText("Của bạn");
    await expect(heroSection).toContainText("Bộ sưu tập mới 2024");
    await expect(heroSection).toContainText(
      "Tìm kiếm những xu hướng thời trang mới nhất"
    );
  });

  test("should display hero action buttons", async ({ page }) => {
    const shopNowBtn = page.locator(
      'a[href="/products"]:has-text("Mua sắm ngay")'
    );
    const categoriesBtn = page.locator(
      'a[href="/categories"]:has-text("Xem danh mục")'
    );

    await expect(shopNowBtn).toBeVisible();
    await expect(categoriesBtn).toBeVisible();
  });

  test("should display statistics section", async ({ page }) => {
    await expect(page.locator("text=1000+")).toBeVisible();
    await expect(page.locator("text=50+")).toBeVisible();
    await expect(page.locator("text=Thương hiệu")).toBeVisible();
    await expect(page.locator("text=10k+")).toBeVisible();
    await expect(page.locator("text=Khách hàng")).toBeVisible();
  });

  test("should display categories section", async ({ page }) => {
    const categoriesSection = page
      .locator("text=Danh mục sản phẩm")
      .locator("..")
      .locator("..");

    await expect(categoriesSection.locator("h2")).toContainText(
      "Danh mục sản phẩm"
    );
    await expect(categoriesSection).toContainText(
      "Khám phá bộ sưu tập đa dạng"
    );

    // Check category cards
    await expect(page.locator('a[href="/categories/ao-thun"]')).toBeVisible();
    await expect(page.locator('a[href="/categories/vay-dam"]')).toBeVisible();
    await expect(
      page.locator('a[href="/categories/quan-jeans"]')
    ).toBeVisible();
    await expect(page.locator('a[href="/categories/ao-khoac"]')).toBeVisible();
    await expect(page.locator('a[href="/categories/phu-kien"]')).toBeVisible();
    await expect(page.locator('a[href="/categories/giay-dep"]')).toBeVisible();
  });

  test("should navigate to category pages when clicked", async ({ page }) => {
    await page.locator('a[href="/categories/ao-thun"]').click();
    await expect(page).toHaveURL("/categories/ao-thun");
  });

  test("should display featured products section", async ({ page }) => {
    const featuredSection = page
      .locator("text=Sản phẩm nổi bật")
      .locator("..")
      .locator("..");

    await expect(featuredSection.locator("h2")).toContainText(
      "Sản phẩm nổi bật"
    );
    await expect(featuredSection).toContainText(
      "Khám phá những sản phẩm được yêu thích nhất"
    );

    // Check for product cards
    const productCards = page.locator('a[href^="/products/"]');
    await expect(productCards).toHaveCount(6); // Based on the snapshot showing 6 products
  });

  test("should display product information correctly", async ({ page }) => {
    // Check first product
    const firstProduct = page.locator(
      'a[href="/products/ao-thun-cotton-premium"]'
    );
    await expect(firstProduct).toContainText("Áo thun cotton premium");

    // Check for price display - use first() to avoid strict mode violation
    await expect(page.locator("text=199.000 ₫").first()).toBeVisible();
    await expect(page.locator("text=299.000 ₫").first()).toBeVisible();

    // Check for ratings
    await expect(page.locator("text=4.8 (124)")).toBeVisible();
  });

  test("should have working add to cart buttons", async ({ page }) => {
    const addToCartButtons = page.locator('button:has-text("Thêm vào giỏ")');
    await expect(addToCartButtons.first()).toBeVisible();

    // Click first add to cart button
    await addToCartButtons.first().click();
    // Note: This would need to be updated based on actual cart functionality
  });

  test("should display newsletter section", async ({ page }) => {
    const newsletterSection = page
      .locator("text=Đăng ký nhận tin")
      .locator("..")
      .locator("..");

    await expect(newsletterSection.locator("h2")).toContainText(
      "Đăng ký nhận tin và nhận ngay"
    );
    await expect(newsletterSection.locator("h2")).toContainText(
      "voucher 100.000đ"
    );
    await expect(newsletterSection).toContainText(
      "Cập nhật những xu hướng thời trang mới nhất"
    );

    // Check newsletter form
    const emailInput = newsletterSection.locator('input[placeholder*="email"]');
    const subscribeBtn = newsletterSection.locator(
      'button:has-text("Đăng ký")'
    );

    await expect(emailInput).toBeVisible();
    await expect(subscribeBtn).toBeVisible();
  });

  test("should test newsletter subscription", async ({ page }) => {
    // Use the footer newsletter form which is more accessible
    const emailInput = page.locator('input[placeholder*="Email của bạn"]');
    const subscribeBtn = page.locator('button:has-text("Đăng ký")');

    await emailInput.fill("<EMAIL>");
    await expect(emailInput).toHaveValue("<EMAIL>");

    await subscribeBtn.click();
    // Note: Would need to verify success message or behavior
  });

  test("should display footer correctly", async ({ page }) => {
    const footer = page.locator("contentinfo");

    await expect(footer).toContainText("NS Shop");
    await expect(footer).toContainText("Khám phá xu hướng thời trang mới nhất");

    // Check footer links
    await expect(footer.locator('a[href="/about"]')).toContainText(
      "Về chúng tôi"
    );
    await expect(footer.locator('a[href="/products"]')).toContainText(
      "Sản phẩm"
    );
    await expect(footer.locator('a[href="/categories"]')).toContainText(
      "Danh mục"
    );
    await expect(footer.locator('a[href="/contact"]')).toContainText("Liên hệ");

    // Check support links
    await expect(footer.locator('a[href="/shipping"]')).toContainText(
      "Chính sách giao hàng"
    );
    await expect(footer.locator('a[href="/returns"]')).toContainText(
      "Đổi trả hàng"
    );
    await expect(footer.locator('a[href="/privacy"]')).toContainText(
      "Chính sách bảo mật"
    );
  });

  test("should display contact information in footer", async ({ page }) => {
    const footer = page.locator("contentinfo");

    await expect(footer).toContainText("123 Đường ABC, Quận 1, TP.HCM");
    await expect(footer).toContainText("+84 123 456 789");
    await expect(footer).toContainText("<EMAIL>");
  });

  test("should display copyright information", async ({ page }) => {
    await expect(
      page.locator("text=© 2024 NS Shop. Tất cả quyền được bảo lưu.")
    ).toBeVisible();
  });

  test("should navigate to products page from hero button", async ({
    page,
  }) => {
    await page
      .locator('a[href="/products"]:has-text("Mua sắm ngay")')
      .click({ force: true });
    await expect(page).toHaveURL("/products");
  });

  test("should navigate to categories page from hero button", async ({
    page,
  }) => {
    await page
      .locator('a[href="/categories"]:has-text("Xem danh mục")')
      .click({ force: true });
    await expect(page).toHaveURL("/categories");
  });
});
