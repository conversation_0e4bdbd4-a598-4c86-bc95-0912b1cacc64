const fs = require('fs');
const FormData = require('form-data');
const fetch = require('node-fetch');

async function testUploadAPI() {
  try {
    console.log('Testing upload API...');
    
    // First, let's test without authentication to see the error
    console.log('\n1. Testing without authentication:');
    
    const formData = new FormData();
    
    // Create a simple test file
    const testContent = Buffer.from('Test image content', 'utf8');
    formData.append('file', testContent, {
      filename: 'test.txt',
      contentType: 'text/plain'
    });
    formData.append('folder', 'uploads');
    
    const response = await fetch('http://localhost:6002/api/admin/media/upload', {
      method: 'POST',
      body: formData,
    });
    
    const result = await response.json();
    console.log('Response status:', response.status);
    console.log('Response body:', result);
    
    if (response.status === 401) {
      console.log('\n✅ Expected: Authentication required');
      console.log('This confirms the API is working but requires admin authentication');
    }
    
  } catch (error) {
    console.error('Test failed:', error);
  }
}

testUploadAPI();
