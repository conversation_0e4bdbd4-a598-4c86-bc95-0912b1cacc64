"use client";

import {
  Shield,
  Truck,
  RotateCcw,
  Headphones,
  Award,
  CreditCard,
} from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { useSettingsContext } from "@/contexts/SettingsContext";

export function WhyChooseUs() {
  const { settings } = useSettingsContext();

  const features = [
    {
      icon: <Shield className="h-8 w-8 text-blue-500" />,
      title: "<PERSON><PERSON>o đảm chất lượng",
      description:
        "Tất cả sản phẩm đều được kiểm tra chất lượng nghiêm ngặt trước khi giao đến tay khách hàng",
    },
    {
      icon: <Truck className="h-8 w-8 text-green-500" />,
      title: `Giao hàng ${settings.shippingSettings.estimatedDelivery}`,
      description: `<PERSON><PERSON><PERSON> phí vận chuyển cho đơn hàng từ ${settings.shippingSettings.freeShippingThreshold.toLocaleString(
        "vi-VN"
      )}đ trên toàn quốc`,
    },
    {
      icon: <RotateCcw className="h-8 w-8 text-orange-500" />,
      title: `Đổi trả 30 ngày`,
      description:
        "Chính sách đổi trả linh hoạt, không cần lý do trong vòng 30 ngày đầu tiên",
    },
    {
      icon: <Headphones className="h-8 w-8 text-purple-500" />,
      title: "Hỗ trợ 24/7",
      description: `Đội ngũ tư vấn chuyên nghiệp sẵn sàng hỗ trợ bạn qua ${settings.contactPhone} hoặc email`,
    },
    {
      icon: <Award className="h-8 w-8 text-yellow-500" />,
      title: "Thương hiệu uy tín",
      description:
        "Được tin tưởng bởi hàng nghìn khách hàng với đánh giá 4.9/5 sao",
    },
    {
      icon: <CreditCard className="h-8 w-8 text-fashion-500" />,
      title: "Thanh toán an toàn",
      description:
        "Hỗ trợ nhiều phương thức thanh toán: COD, chuyển khoản, ví điện tử",
    },
  ];

  return (
    <section className="py-20 bg-gradient-to-br from-background via-muted/20 to-background">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl lg:text-4xl font-bold mb-4">
            Vì sao khách hàng tin tưởng NS Shop?
          </h2>
          <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
            Chúng tôi cam kết mang đến trải nghiệm mua sắm tuyệt vời nhất với
            dịch vụ chuyên nghiệp và chính sách khách hàng ưu việt
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <Card
              key={index}
              className="group hover:shadow-xl transition-all duration-300 border-0 bg-white/80 dark:bg-card/80 backdrop-blur-sm hover:scale-105"
            >
              <CardContent className="p-8 text-center">
                <div className="flex justify-center mb-6">
                  <div className="p-4 bg-muted/50 rounded-2xl group-hover:bg-muted transition-colors">
                    {feature.icon}
                  </div>
                </div>
                <h3 className="text-xl font-bold mb-4 group-hover:text-fashion-600 transition-colors">
                  {feature.title}
                </h3>
                <p className="text-muted-foreground leading-relaxed">
                  {feature.description}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Trust Indicators */}
        <div className="mt-16 text-center">
          <div className="inline-flex items-center gap-8 p-6 bg-white/50 dark:bg-card/50 backdrop-blur-sm rounded-2xl">
            <div className="flex items-center gap-2">
              <Shield className="h-5 w-5 text-green-500" />
              <span className="text-sm font-medium">SSL Secured</span>
            </div>
            <div className="flex items-center gap-2">
              <Award className="h-5 w-5 text-yellow-500" />
              <span className="text-sm font-medium">Verified Store</span>
            </div>
            <div className="flex items-center gap-2">
              <CreditCard className="h-5 w-5 text-blue-500" />
              <span className="text-sm font-medium">Safe Payment</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
