import "@/app/globals.css";
import { Metadata, Viewport } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from "next/font/google";
import { AuthProvider } from "@/providers/auth-provider";
import { SettingsProvider } from "@/contexts/SettingsContext";
import { ToastProvider } from "@/components/ui/toast";
import { ToastInit } from "@/components/ui/toast-init";
import { ConditionalCartProvider } from "@/providers/conditional-cart-provider";
import { DynamicMetadata } from "@/components/seo/dynamic-metadata";

const geist = Geist({
  subsets: ["latin"],
  variable: "--font-geist",
  preload: true,
});

const geistMono = Geist_Mono({
  subsets: ["latin"],
  variable: "--font-geist-mono",
  preload: true,
});

export const metadata: Metadata = {
  title: {
    default: "NS Shop - Thời trang trực tuyến hàng đầu <PERSON>",
    template: "%s | NS Shop",
  },
  description:
    "Khám phá xu hướng thời trang mới nhất tại NS Shop. Cửa hàng thời trang trực tuyến với hơn 10,000+ sản phẩm chất lượng cao, giá cả hợp lý. Miễn phí vận chuyển toàn quốc.",
  keywords: [
    "thời trang",
    "quần áo",
    "giày dép",
    "phụ kiện",
    "thời trang nữ",
    "thời trang nam",
    "mua sắm trực tuyến",
    "NS Shop",
    "fashion",
    "clothing",
    "shoes",
    "accessories",
  ],
  authors: [{ name: "NS Shop Team", url: "https://nsshop.com" }],
  creator: "NS Shop",
  publisher: "NS Shop",
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  openGraph: {
    type: "website",
    locale: "vi_VN",
    url: "https://nsshop.com",
    siteName: "NS Shop",
    title: "NS Shop - Thời trang trực tuyến hàng đầu Việt Nam",
    description:
      "Khám phá xu hướng thời trang mới nhất tại NS Shop. Cửa hàng thời trang trực tuyến với hơn 10,000+ sản phẩm chất lượng cao, giá cả hợp lý.",
    images: [
      {
        url: "/og-image.jpg",
        width: 1200,
        height: 630,
        alt: "NS Shop - Thời trang trực tuyến",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    site: "@nsshop",
    creator: "@nsshop",
    title: "NS Shop - Thời trang trực tuyến hàng đầu Việt Nam",
    description:
      "Khám phá xu hướng thời trang mới nhất tại NS Shop. Cửa hàng thời trang trực tuyến với hơn 10,000+ sản phẩm chất lượng cao.",
    images: ["/og-image.jpg"],
  },
  verification: {
    google: "your-google-verification-code",
    yandex: "your-yandex-verification-code",
    yahoo: "your-yahoo-verification-code",
  },
  alternates: {
    canonical: "https://nsshop.com",
    languages: {
      "vi-VN": "https://nsshop.com",
      "en-US": "https://nsshop.com/en",
    },
  },
  category: "fashion",
  classification: "Business",
  referrer: "origin-when-cross-origin",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL("https://nsshop.com"),
  icons: {
    icon: [
      { url: "/favicon.ico", sizes: "any" },
      { url: "/icon-16x16.png", sizes: "16x16", type: "image/png" },
      { url: "/icon-32x32.png", sizes: "32x32", type: "image/png" },
      { url: "/icon-192x192.png", sizes: "192x192", type: "image/png" },
      { url: "/icon-512x512.png", sizes: "512x512", type: "image/png" },
    ],
    apple: [
      { url: "/apple-touch-icon.png", sizes: "180x180", type: "image/png" },
    ],
    shortcut: "/favicon.ico",
  },
};

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
  themeColor: [
    { media: "(prefers-color-scheme: light)", color: "#ec4899" },
    { media: "(prefers-color-scheme: dark)", color: "#ec4899" },
  ],
  colorScheme: "light dark",
};

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="vi" suppressHydrationWarning>
      <head>
        {/* Preconnect to external domains for performance */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link
          rel="preconnect"
          href="https://fonts.gstatic.com"
          crossOrigin="anonymous"
        />
        <link rel="preconnect" href="https://www.google-analytics.com" />
        <link rel="preconnect" href="https://www.googletagmanager.com" />

        {/* DNS prefetch for better performance */}
        <link rel="dns-prefetch" href="https://fonts.googleapis.com" />
        <link rel="dns-prefetch" href="https://fonts.gstatic.com" />

        {/* PWA and mobile optimization */}
        <link rel="manifest" href="/manifest.json" />
        <meta name="mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="apple-mobile-web-app-title" content="NS Shop" />
        <meta name="application-name" content="NS Shop" />
        <meta name="msapplication-TileColor" content="#ec4899" />
        <meta name="msapplication-config" content="/browserconfig.xml" />

        {/* Security headers */}
        <meta httpEquiv="X-Content-Type-Options" content="nosniff" />
        <meta httpEquiv="X-Frame-Options" content="DENY" />
        <meta httpEquiv="X-XSS-Protection" content="1; mode=block" />
        <meta name="referrer" content="origin-when-cross-origin" />

        {/* Performance hints */}
        <link
          rel="preload"
          href="/fonts/geist.woff2"
          as="font"
          type="font/woff2"
          crossOrigin="anonymous"
        />

        {/* Structured data for SEO */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "Organization",
              name: "NS Shop",
              url: "https://nsshop.com",
              logo: "https://nsshop.com/logo.png",
              description: "Cửa hàng thời trang trực tuyến hàng đầu Việt Nam",
              address: {
                "@type": "PostalAddress",
                streetAddress: "123 Đường ABC",
                addressLocality: "Quận 1",
                addressRegion: "TP.HCM",
                addressCountry: "VN",
              },
              contactPoint: {
                "@type": "ContactPoint",
                telephone: "+84-123-456-789",
                contactType: "customer service",
                email: "<EMAIL>",
              },
              sameAs: [
                "https://facebook.com/nsshop",
                "https://instagram.com/nsshop",
                "https://twitter.com/nsshop",
              ],
            }),
          }}
        />
      </head>
      <body
        className={`${geist.variable} ${geistMono.variable} min-h-screen flex flex-col antialiased`}
        role="application"
        aria-label="NS Shop Fashion Store"
        suppressHydrationWarning
      >
        <AuthProvider>
          <SettingsProvider>
            <ConditionalCartProvider>
              <ToastProvider>
                <ToastInit />
                {children}
              </ToastProvider>
            </ConditionalCartProvider>
          </SettingsProvider>
        </AuthProvider>
      </body>
    </html>
  );
}
