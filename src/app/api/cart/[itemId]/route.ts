import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { getServerSession } from "next-auth";
import { authOptions } from "../../auth/[...nextauth]/route";
import { z } from "zod";

const updateCartItemSchema = z.object({
  quantity: z.number().int().min(0, "Số lượng không được âm"),
});

// PUT /api/cart/[itemId] - Cập nhật số lượng sản phẩm trong giỏ hàng
export async function PUT(
  request: NextRequest,
  { params }: { params: { itemId: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Vui lòng đăng nhập" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { quantity } = updateCartItemSchema.parse(body);

    // Tìm cart item và kiểm tra quyền sở hữu
    const cartItem = await prisma.cartItem.findUnique({
      where: { id: params.itemId },
      include: {
        cart: true,
        product: true,
      },
    });

    if (!cartItem) {
      return NextResponse.json(
        { error: "Không tìm thấy sản phẩm trong giỏ hàng" },
        { status: 404 }
      );
    }

    if (cartItem.cart.userId !== session.user.id) {
      return NextResponse.json(
        { error: "Không có quyền truy cập" },
        { status: 403 }
      );
    }

    // Nếu quantity = 0, xóa item
    if (quantity === 0) {
      await prisma.cartItem.delete({
        where: { id: params.itemId },
      });

      return NextResponse.json({
        message: "Xóa sản phẩm khỏi giỏ hàng thành công",
      });
    }

    // Kiểm tra stock
    if (cartItem.product.stock < quantity) {
      return NextResponse.json(
        { error: "Không đủ hàng trong kho" },
        { status: 400 }
      );
    }

    // Cập nhật số lượng
    const updatedItem = await prisma.cartItem.update({
      where: { id: params.itemId },
      data: { quantity },
      include: {
        product: {
          select: {
            id: true,
            name: true,
            price: true,
            salePrice: true,
            media: {
              include: {
                media: {
                  select: {
                    id: true,
                    url: true,
                    alt: true,
                  },
                },
              },
              take: 1,
            },
            slug: true,
            stock: true,
            status: true,
          },
        },
      },
    });

    return NextResponse.json({
      message: "Cập nhật giỏ hàng thành công",
      item: updatedItem,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      );
    }

    console.error("Update cart item error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi cập nhật giỏ hàng" },
      { status: 500 }
    );
  }
}

// DELETE /api/cart/[itemId] - Xóa sản phẩm khỏi giỏ hàng
export async function DELETE(
  request: NextRequest,
  { params }: { params: { itemId: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Vui lòng đăng nhập" },
        { status: 401 }
      );
    }

    // Tìm cart item và kiểm tra quyền sở hữu
    const cartItem = await prisma.cartItem.findUnique({
      where: { id: params.itemId },
      include: {
        cart: true,
      },
    });

    if (!cartItem) {
      return NextResponse.json(
        { error: "Không tìm thấy sản phẩm trong giỏ hàng" },
        { status: 404 }
      );
    }

    if (cartItem.cart.userId !== session.user.id) {
      return NextResponse.json(
        { error: "Không có quyền truy cập" },
        { status: 403 }
      );
    }

    // Xóa item
    await prisma.cartItem.delete({
      where: { id: params.itemId },
    });

    return NextResponse.json({
      message: "Xóa sản phẩm khỏi giỏ hàng thành công",
    });
  } catch (error) {
    console.error("Delete cart item error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi xóa sản phẩm khỏi giỏ hàng" },
      { status: 500 }
    );
  }
}
