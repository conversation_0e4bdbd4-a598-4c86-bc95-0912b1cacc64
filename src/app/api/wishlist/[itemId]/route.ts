import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { getServerSession } from "next-auth";
import { authOptions } from "../../auth/[...nextauth]/route";

// DELETE /api/wishlist/[itemId] - <PERSON><PERSON><PERSON> sản phẩm khỏi danh sách yêu thích
export async function DELETE(
  request: NextRequest,
  { params }: { params: { itemId: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session || !session.user || !session.user.id) {
      return NextResponse.json(
        { error: "Vui lòng đăng nhập" },
        { status: 401 }
      );
    }

    // Tìm wishlist item và kiểm tra quyền sở hữu
    const wishlistItem = await prisma.wishlistItem.findUnique({
      where: { id: params.itemId },
    });

    if (!wishlistItem) {
      return NextResponse.json(
        { error: "<PERSON>hông tìm thấy sản phẩm trong danh sách yêu thích" },
        { status: 404 }
      );
    }

    // Kiểm tra quyền sở hữu
    if (wishlistItem.userId !== session.user.id) {
      return NextResponse.json(
        { error: "Bạn không có quyền xóa sản phẩm này" },
        { status: 403 }
      );
    }

    // Xóa khỏi wishlist
    await prisma.wishlistItem.delete({
      where: { id: params.itemId },
    });

    return NextResponse.json({
      message: "Đã xóa khỏi danh sách yêu thích",
    });
  } catch (error) {
    console.error("Remove from wishlist error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi xóa khỏi danh sách yêu thích" },
      { status: 500 }
    );
  }
}
