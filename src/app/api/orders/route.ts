import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { getServerSession } from "next-auth";
import { authOptions } from "../auth/[...nextauth]/route";
import { z } from "zod";
import { emailService } from "@/lib/email-service";
import type { OrderConfirmationEmailData } from "@/lib/email/templates";

const createOrderSchema = z.object({
  shippingAddress: z.object({
    fullName: z.string().min(1, "Họ tên là bắt buộc"),
    phone: z.string().min(1, "Số điện thoại là bắt buộc"),
    address: z.string().min(1, "Địa chỉ là bắt buộc"),
    ward: z.string().min(1, "Phường/Xã là bắt buộc"),
    district: z.string().min(1, "Quận/Huyện là bắt buộc"),
    province: z.string().min(1, "Tỉnh/Thành phố là bắt buộc"),
  }),
  billingAddress: z
    .object({
      fullName: z.string().min(1, "Họ tên là bắt buộc"),
      phone: z.string().min(1, "Số điện thoại là bắt buộc"),
      address: z.string().min(1, "Địa chỉ là bắt buộc"),
      ward: z.string().min(1, "Phường/Xã là bắt buộc"),
      district: z.string().min(1, "Quận/Huyện là bắt buộc"),
      province: z.string().min(1, "Tỉnh/Thành phố là bắt buộc"),
    })
    .optional(),
  paymentMethod: z.enum(["COD", "BANK_TRANSFER", "CREDIT_CARD"]),
  notes: z.string().optional(),
});

// GET /api/orders - Lấy danh sách đơn hàng của user
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Vui lòng đăng nhập" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const status = searchParams.get("status");

    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {
      userId: session.user.id,
    };

    if (status) {
      where.status = status;
    }

    // Get orders with pagination
    const [orders, total] = await Promise.all([
      prisma.order.findMany({
        where,
        include: {
          items: {
            include: {
              product: {
                select: {
                  id: true,
                  name: true,
                  media: {
                    include: {
                      media: {
                        select: {
                          id: true,
                          url: true,
                          alt: true,
                        },
                      },
                    },
                    take: 1,
                  },
                  slug: true,
                },
              },
            },
          },
        },
        orderBy: {
          createdAt: "desc",
        },
        skip,
        take: limit,
      }),
      prisma.order.count({ where }),
    ]);

    return NextResponse.json({
      orders,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error("Get orders error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi lấy danh sách đơn hàng" },
      { status: 500 }
    );
  }
}

// POST /api/orders - Tạo đơn hàng mới
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Vui lòng đăng nhập" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const data = createOrderSchema.parse(body);

    // Lấy giỏ hàng của user
    const cart = await prisma.cart.findUnique({
      where: { userId: session.user.id },
      include: {
        items: {
          include: {
            product: true,
          },
        },
      },
    });

    if (!cart || cart.items.length === 0) {
      return NextResponse.json({ error: "Giỏ hàng trống" }, { status: 400 });
    }

    // Kiểm tra stock và tính tổng tiền
    let total = 0;
    const orderItems: Array<{
      productId: string;
      quantity: number;
      price: number;
      total: number;
    }> = [];

    for (const item of cart.items) {
      if (item.product.status !== "ACTIVE") {
        return NextResponse.json(
          { error: `Sản phẩm ${item.product.name} không còn khả dụng` },
          { status: 400 }
        );
      }

      if (item.product.stock < item.quantity) {
        return NextResponse.json(
          { error: `Sản phẩm ${item.product.name} không đủ hàng trong kho` },
          { status: 400 }
        );
      }

      const price = item.product.salePrice || item.product.price;
      total += price * item.quantity;

      orderItems.push({
        productId: item.productId,
        quantity: item.quantity,
        price: price,
        total: price * item.quantity,
      });
    }

    // Tạo đơn hàng trong transaction
    const order = await prisma.$transaction(async (tx) => {
      // Tạo đơn hàng
      const newOrder = await tx.order.create({
        data: {
          userId: session.user.id,
          total,
          shippingAddress: data.shippingAddress,
          billingAddress: data.billingAddress,
          paymentMethod: data.paymentMethod,
          notes: data.notes,
          items: {
            create: orderItems,
          },
        },
        include: {
          items: {
            include: {
              product: {
                select: {
                  id: true,
                  name: true,
                  media: {
                    include: {
                      media: {
                        select: {
                          id: true,
                          url: true,
                          alt: true,
                        },
                      },
                    },
                    take: 1,
                  },
                  slug: true,
                },
              },
            },
          },
        },
      });

      // Cập nhật stock và inventory
      for (const item of cart.items) {
        // Update product stock
        await tx.product.update({
          where: { id: item.productId },
          data: {
            stock: {
              decrement: item.quantity,
            },
          },
        });

        // Update inventory entry if exists
        const inventoryEntry = await tx.inventoryEntry.findUnique({
          where: { productId: item.productId },
        });

        if (inventoryEntry) {
          // Update inventory quantities
          await tx.inventoryEntry.update({
            where: { id: inventoryEntry.id },
            data: {
              quantity: {
                decrement: item.quantity,
              },
              available: {
                decrement: item.quantity,
              },
            },
          });

          // Create stock movement record
          await tx.stockMovement.create({
            data: {
              inventoryEntryId: inventoryEntry.id,
              type: "OUT",
              quantity: item.quantity,
              reason: "Bán hàng",
              reference: `ORDER-${newOrder.id}`,
              notes: `Xuất kho cho đơn hàng #${newOrder.id}`,
            },
          });
        }
      }

      // Xóa giỏ hàng
      await tx.cartItem.deleteMany({
        where: { cartId: cart.id },
      });

      return newOrder;
    });

    // Send order confirmation email
    try {
      await emailService.initialize();

      // Get user details
      const user = await prisma.user.findUnique({
        where: { id: session.user.id },
        select: { name: true, email: true },
      });

      if (user) {
        const orderConfirmationData: OrderConfirmationEmailData = {
          recipientName: user.name,
          recipientEmail: user.email,
          order: {
            id: order.id,
            total: order.total,
            status: order.status,
            createdAt: order.createdAt.toISOString(),
            items: order.items.map((item) => ({
              name: item.product.name,
              quantity: item.quantity,
              price: item.price,
              image: item.product.media?.[0]?.media?.url || undefined,
            })),
            shippingAddress: order.shippingAddress
              ? {
                  fullName: (order.shippingAddress as any).fullName,
                  address: (order.shippingAddress as any).address,
                  city: `${(order.shippingAddress as any).ward}, ${(order.shippingAddress as any).district}, ${(order.shippingAddress as any).province}`,
                  postalCode: "", // Add postal code if available in your schema
                  phone: (order.shippingAddress as any).phone,
                }
              : {
                  fullName: "N/A",
                  address: "N/A",
                  city: "N/A",
                  postalCode: "",
                  phone: "N/A",
                },
          },
          trackingUrl: `${process.env.NEXT_PUBLIC_APP_URL}/orders/${order.id}`,
        };

        await emailService.sendOrderConfirmationEmail(orderConfirmationData);
        console.log(
          `Order confirmation email sent to ${user.email} for order ${order.id}`
        );
      }
    } catch (error) {
      console.error("Failed to send order confirmation email:", error);
      // Don't fail order creation if email fails
    }

    return NextResponse.json(
      {
        message: "Đặt hàng thành công",
        order,
      },
      { status: 201 }
    );
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      );
    }

    console.error("Create order error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi tạo đơn hàng" },
      { status: 500 }
    );
  }
}
