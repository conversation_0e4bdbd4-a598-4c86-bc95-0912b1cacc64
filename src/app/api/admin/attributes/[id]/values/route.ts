import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { z } from "zod";
import { jwtVerify } from "jose";

// Helper function to verify admin authentication
async function verifyAdminAuth(request: NextRequest) {
  try {
    const adminToken = request.cookies.get("admin-session")?.value;

    if (!adminToken) {
      return null;
    }

    const secret = new TextEncoder().encode(
      process.env.NEXTAUTH_SECRET || "fallback-secret"
    );

    const { payload } = await jwtVerify(adminToken, secret);

    if (payload.role !== "ADMIN" && payload.role !== "MODERATOR") {
      return null;
    }

    return {
      id: payload.id as string,
      role: payload.role as string,
      type: "admin" as const,
    };
  } catch {
    return null;
  }
}

// Validation schema for attribute value
const attributeValueSchema = z.object({
  value: z.string().min(1, "<PERSON>i<PERSON> trị là bắt buộc"),
  slug: z.string().optional(),
  sortOrder: z.number().default(0),
});

// GET /api/admin/attributes/[id]/values - <PERSON><PERSON><PERSON> danh sách values của attribute
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const adminUser = await verifyAdminAuth(request);

    if (!adminUser) {
      return NextResponse.json(
        { error: "Không có quyền truy cập" },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "50");
    const search = searchParams.get("search") || "";
    const sortBy = searchParams.get("sortBy") || "sortOrder";
    const sortOrder = searchParams.get("sortOrder") || "asc";

    const skip = (page - 1) * limit;

    // Check if attribute exists
    const attribute = await prisma.attribute.findUnique({
      where: { id: params.id },
    });

    if (!attribute) {
      return NextResponse.json(
        { error: "Không tìm thấy thuộc tính" },
        { status: 404 }
      );
    }

    // Build where clause
    const where: any = {
      attributeId: params.id,
    };

    if (search) {
      where.value = {
        contains: search,
        mode: "insensitive",
      };
    }

    // Get values with pagination
    const [values, total] = await Promise.all([
      prisma.attributeValue.findMany({
        where,
        include: {
          _count: {
            select: {
              products: true,
            },
          },
        },
        orderBy: {
          [sortBy]: sortOrder,
        },
        skip,
        take: limit,
      }),
      prisma.attributeValue.count({ where }),
    ]);

    const pagination = {
      page,
      limit,
      total,
      pages: Math.ceil(total / limit),
    };

    return NextResponse.json({
      data: values,
      pagination,
    });
  } catch (error) {
    console.error("Error fetching attribute values:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi tải danh sách giá trị" },
      { status: 500 }
    );
  }
}

// POST /api/admin/attributes/[id]/values - Tạo value mới cho attribute
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const adminUser = await verifyAdminAuth(request);

    if (!adminUser) {
      return NextResponse.json(
        { error: "Không có quyền truy cập" },
        { status: 403 }
      );
    }

    const body = await request.json();
    const validatedData = attributeValueSchema.parse(body);

    // Check if attribute exists
    const attribute = await prisma.attribute.findUnique({
      where: { id: params.id },
    });

    if (!attribute) {
      return NextResponse.json(
        { error: "Không tìm thấy thuộc tính" },
        { status: 404 }
      );
    }

    // Generate slug if not provided
    if (!validatedData.slug) {
      validatedData.slug = validatedData.value
        .toLowerCase()
        .replace(/\s+/g, "-")
        .replace(/[àáạảãâầấậẩẫăằắặẳẵ]/g, "a")
        .replace(/[èéẹẻẽêềếệểễ]/g, "e")
        .replace(/[ìíịỉĩ]/g, "i")
        .replace(/[òóọỏõôồốộổỗơờớợởỡ]/g, "o")
        .replace(/[ùúụủũưừứựửữ]/g, "u")
        .replace(/[ỳýỵỷỹ]/g, "y")
        .replace(/[đ]/g, "d")
        .replace(/[^a-z0-9-]/g, "");
    }

    // Check for duplicate value
    const existingValue = await prisma.attributeValue.findFirst({
      where: {
        attributeId: params.id,
        OR: [{ value: validatedData.value }, { slug: validatedData.slug }],
      },
    });

    if (existingValue) {
      return NextResponse.json(
        { error: "Giá trị hoặc slug đã tồn tại" },
        { status: 400 }
      );
    }

    // Create attribute value
    const attributeValue = await prisma.attributeValue.create({
      data: {
        attributeId: params.id,
        value: validatedData.value,
        slug: validatedData.slug,
        sortOrder: validatedData.sortOrder,
      },
      include: {
        _count: {
          select: {
            products: true,
          },
        },
      },
    });

    return NextResponse.json(attributeValue, { status: 201 });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Dữ liệu không hợp lệ", details: error.errors },
        { status: 400 }
      );
    }

    console.error("Error creating attribute value:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi tạo giá trị" },
      { status: 500 }
    );
  }
}
