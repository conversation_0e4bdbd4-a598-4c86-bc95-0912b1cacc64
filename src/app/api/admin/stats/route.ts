import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { jwtVerify } from "jose";

// Helper function to verify admin JWT token
async function verifyAdminToken(request: NextRequest) {
  try {
    const adminToken = request.cookies.get("admin-session")?.value;
    if (!adminToken) return null;

    const secret = new TextEncoder().encode(
      process.env.NEXTAUTH_SECRET || "fallback-secret"
    );
    const { payload } = await jwtVerify(adminToken, secret);
    return payload;
  } catch {
    return null;
  }
}

// GET /api/admin/stats - <PERSON><PERSON><PERSON> thống kê tổng quan cho admin dashboard
export async function GET(request: NextRequest) {
  try {
    const adminToken = await verifyAdminToken(request);

    if (
      !adminToken ||
      (adminToken.role !== "ADMIN" && adminToken.role !== "MODERATOR")
    ) {
      return NextResponse.json(
        { error: "<PERSON><PERSON><PERSON>ng có quyền truy cập" },
        { status: 403 }
      );
    }

    // Get basic counts
    const [
      totalProducts,
      activeProducts,
      totalCategories,
      totalOrders,
      totalUsers,
      totalRevenue,
      recentOrders,
      topProducts,
      lowStockProducts,
    ] = await Promise.all([
      // Total products
      prisma.product.count(),

      // Active products
      prisma.product.count({
        where: { status: "ACTIVE" },
      }),

      // Total categories
      prisma.category.count(),

      // Total orders
      prisma.order.count(),

      // Total users
      prisma.user.count(),

      // Total revenue (from completed orders)
      prisma.order.aggregate({
        where: {
          status: "DELIVERED",
        },
        _sum: {
          total: true,
        },
      }),

      // Recent orders (last 10)
      prisma.order.findMany({
        take: 10,
        orderBy: {
          createdAt: "desc",
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      }),

      // Top selling products (by order items count)
      prisma.product.findMany({
        take: 5,
        include: {
          _count: {
            select: {
              orderItems: true,
            },
          },
          category: {
            select: {
              name: true,
            },
          },
          media: {
            include: {
              media: {
                select: {
                  id: true,
                  url: true,
                  alt: true,
                },
              },
            },
            take: 1,
          },
        },
        orderBy: {
          orderItems: {
            _count: "desc",
          },
        },
      }),

      // Low stock products (stock < 10)
      prisma.product.findMany({
        where: {
          stock: {
            lt: 10,
          },
          status: "ACTIVE",
        },
        take: 10,
        include: {
          category: {
            select: {
              name: true,
            },
          },
          media: {
            include: {
              media: {
                select: {
                  id: true,
                  url: true,
                  alt: true,
                },
              },
            },
            take: 1,
          },
        },
        orderBy: {
          stock: "asc",
        },
      }),
    ]);

    // Calculate growth rates (compared to last month)
    const lastMonth = new Date();
    lastMonth.setMonth(lastMonth.getMonth() - 1);

    const [
      lastMonthProducts,
      lastMonthOrders,
      lastMonthUsers,
      lastMonthRevenue,
    ] = await Promise.all([
      prisma.product.count({
        where: {
          createdAt: {
            lt: lastMonth,
          },
        },
      }),

      prisma.order.count({
        where: {
          createdAt: {
            lt: lastMonth,
          },
        },
      }),

      prisma.user.count({
        where: {
          createdAt: {
            lt: lastMonth,
          },
        },
      }),

      prisma.order.aggregate({
        where: {
          status: "DELIVERED",
          createdAt: {
            lt: lastMonth,
          },
        },
        _sum: {
          total: true,
        },
      }),
    ]);

    // Calculate growth percentages
    const calculateGrowth = (current: number, previous: number) => {
      if (previous === 0) return current > 0 ? 100 : 0;
      return Math.round(((current - previous) / previous) * 100);
    };

    const stats = {
      overview: {
        totalProducts: {
          value: totalProducts,
          growth: calculateGrowth(totalProducts, lastMonthProducts),
        },
        activeProducts: {
          value: activeProducts,
          percentage:
            totalProducts > 0
              ? Math.round((activeProducts / totalProducts) * 100)
              : 0,
        },
        totalCategories: {
          value: totalCategories,
        },
        totalOrders: {
          value: totalOrders,
          growth: calculateGrowth(totalOrders, lastMonthOrders),
        },
        totalUsers: {
          value: totalUsers,
          growth: calculateGrowth(totalUsers, lastMonthUsers),
        },
        totalRevenue: {
          value: totalRevenue._sum?.total || 0,
          growth: calculateGrowth(
            totalRevenue._sum?.total || 0,
            lastMonthRevenue._sum?.total || 0
          ),
        },
      },
      recentOrders: recentOrders.map((order) => ({
        id: order.id,
        total: order.total,
        status: order.status,
        createdAt: order.createdAt,
        user: order.user,
      })),
      topProducts: topProducts.map((product) => ({
        id: product.id,
        name: product.name,
        price: product.price,
        stock: product.stock,
        category: product.category.name,
        orderCount: product._count.orderItems,
        image: product.media?.[0]?.media?.url || null,
      })),
      lowStockProducts: lowStockProducts.map((product) => ({
        id: product.id,
        name: product.name,
        stock: product.stock,
        category: product.category.name,
        image: product.media?.[0]?.media?.url || null,
      })),
    };

    return NextResponse.json(stats);
  } catch (error) {
    console.error("Get admin stats error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi lấy thống kê" },
      { status: 500 }
    );
  }
}
