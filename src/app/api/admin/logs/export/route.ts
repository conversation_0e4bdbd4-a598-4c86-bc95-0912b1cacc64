import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { jwtVerify } from "jose";

// Helper function to verify admin JWT token
async function verifyAdminToken(request: NextRequest) {
  try {
    const adminToken = request.cookies.get("admin-session")?.value;
    if (!adminToken) return null;

    const secret = new TextEncoder().encode(
      process.env.NEXTAUTH_SECRET || "fallback-secret"
    );
    const { payload } = await jwtVerify(adminToken, secret);
    return payload;
  } catch {
    return null;
  }
}

// GET /api/admin/logs/export - Export audit logs to CSV
export async function GET(request: NextRequest) {
  try {
    const adminToken = await verifyAdminToken(request);

    if (!adminToken || adminToken.type !== "admin") {
      return NextResponse.json(
        { error: "Không có quyền truy cập" },
        { status: 403 }
      );
    }

    // Only ADMIN can export audit logs
    if (adminToken.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Chỉ Admin mới có quyền xuất logs" },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const startDate = searchParams.get("startDate");
    const endDate = searchParams.get("endDate");
    const action = searchParams.get("action");
    const resource = searchParams.get("resource");
    const adminId = searchParams.get("adminId");

    // Build where clause
    const where: any = {};

    if (action) {
      where.action = action;
    }

    if (resource) {
      where.resource = resource;
    }

    if (adminId) {
      where.adminId = adminId;
    }

    if (startDate || endDate) {
      where.createdAt = {};
      if (startDate) {
        where.createdAt.gte = new Date(startDate);
      }
      if (endDate) {
        where.createdAt.lte = new Date(endDate);
      }
    }

    const logs = await prisma.auditLog.findMany({
      where,
      include: {
        admin: {
          select: {
            name: true,
            email: true,
            role: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
      take: 10000, // Limit to prevent memory issues
    });

    // Convert to CSV
    const csvHeaders = [
      "Thời gian",
      "Hành động",
      "Tài nguyên",
      "ID tài nguyên",
      "Mô tả",
      "Admin",
      "Email",
      "Vai trò",
      "IP Address",
      "User Agent",
    ];

    const csvRows = logs.map((log) => [
      log.createdAt.toISOString(),
      log.action,
      log.resource,
      log.resourceId || "",
      log.description || "",
      log.admin.name,
      log.admin.email,
      log.admin.role,
      log.ipAddress || "",
      log.userAgent || "",
    ]);

    const csvContent = [
      csvHeaders.join(","),
      ...csvRows.map((row) =>
        row.map((field) => `"${String(field).replace(/"/g, '""')}"`).join(",")
      ),
    ].join("\n");

    // Add BOM for proper UTF-8 encoding in Excel
    const bom = "\uFEFF";
    const csvWithBom = bom + csvContent;

    const filename = `audit-logs-${new Date().toISOString().split("T")[0]}.csv`;

    return new NextResponse(csvWithBom, {
      headers: {
        "Content-Type": "text/csv; charset=utf-8",
        "Content-Disposition": `attachment; filename="${filename}"`,
      },
    });
  } catch (error) {
    console.error("Export audit logs error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi xuất logs" },
      { status: 500 }
    );
  }
}
