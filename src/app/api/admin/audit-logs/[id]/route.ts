import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { jwtVerify } from "jose";

// Helper function to verify admin JWT token
async function verifyAdminToken(request: NextRequest) {
  try {
    const adminToken = request.cookies.get("admin-session")?.value;
    if (!adminToken) return null;

    const secret = new TextEncoder().encode(
      process.env.NEXTAUTH_SECRET || "fallback-secret"
    );
    const { payload } = await jwtVerify(adminToken, secret);
    return payload;
  } catch {
    return null;
  }
}

interface RouteParams {
  params: {
    id: string;
  };
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    // Check admin authentication
    const adminToken = await verifyAdminToken(request);
    if (!adminToken || adminToken.type !== "admin") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = params;

    // Get audit log with admin information
    const auditLog = await prisma.auditLog.findUnique({
      where: { id },
      include: {
        admin: {
          select: {
            id: true,
            name: true,
            email: true,
            avatar: true,
            role: true,
            department: true,
          },
        },
      },
    });

    if (!auditLog) {
      return NextResponse.json(
        { error: "Audit log not found" },
        { status: 404 }
      );
    }

    // Format the response with additional details
    const formattedAuditLog = {
      ...auditLog,
      formattedCreatedAt: auditLog.createdAt.toLocaleString("vi-VN", {
        timeZone: "Asia/Ho_Chi_Minh",
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
        hour: "2-digit",
        minute: "2-digit",
        second: "2-digit",
      }),
      changes:
        auditLog.oldValues && auditLog.newValues
          ? getChanges(auditLog.oldValues, auditLog.newValues)
          : null,
    };

    return NextResponse.json({
      data: formattedAuditLog,
    });
  } catch (error) {
    console.error("Error fetching audit log:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// Helper function to compare old and new values
function getChanges(oldValues: any, newValues: any) {
  const changes: Array<{
    field: string;
    oldValue: any;
    newValue: any;
    type: "added" | "modified" | "removed";
  }> = [];

  const allKeys = new Set([
    ...Object.keys(oldValues || {}),
    ...Object.keys(newValues || {}),
  ]);

  for (const key of allKeys) {
    const oldValue = oldValues?.[key];
    const newValue = newValues?.[key];

    if (oldValue === undefined && newValue !== undefined) {
      changes.push({
        field: key,
        oldValue: null,
        newValue,
        type: "added",
      });
    } else if (oldValue !== undefined && newValue === undefined) {
      changes.push({
        field: key,
        oldValue,
        newValue: null,
        type: "removed",
      });
    } else if (oldValue !== newValue) {
      changes.push({
        field: key,
        oldValue,
        newValue,
        type: "modified",
      });
    }
  }

  return changes;
}
