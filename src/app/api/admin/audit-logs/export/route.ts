import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { z } from "zod";
import { jwtVerify } from "jose";

// Helper function to verify admin JWT token
async function verifyAdminToken(request: NextRequest) {
  try {
    const adminToken = request.cookies.get("admin-session")?.value;
    if (!adminToken) return null;

    const secret = new TextEncoder().encode(
      process.env.NEXTAUTH_SECRET || "fallback-secret"
    );
    const { payload } = await jwtVerify(adminToken, secret);
    return payload;
  } catch {
    return null;
  }
}

// Validation schema for export parameters
const exportQuerySchema = z.object({
  format: z.enum(["csv", "excel"]).default("csv"),
  action: z.string().optional(),
  resource: z.string().optional(),
  adminId: z.string().optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  search: z.string().optional(),
});

export async function POST(request: NextRequest) {
  try {
    // Check admin authentication
    const adminToken = await verifyAdminToken(request);
    if (!adminToken || adminToken.type !== "admin") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const validatedParams = exportQuerySchema.parse(body);

    const { format, action, resource, adminId, startDate, endDate, search } =
      validatedParams;

    // Build where clause (same as in main route)
    const where: any = {};

    if (action) {
      where.action = { contains: action, mode: "insensitive" };
    }

    if (resource) {
      where.resource = { contains: resource, mode: "insensitive" };
    }

    if (adminId) {
      where.adminId = adminId;
    }

    if (startDate || endDate) {
      where.createdAt = {};
      if (startDate) {
        where.createdAt.gte = new Date(startDate);
      }
      if (endDate) {
        where.createdAt.lte = new Date(endDate);
      }
    }

    if (search) {
      where.OR = [
        { action: { contains: search, mode: "insensitive" } },
        { resource: { contains: search, mode: "insensitive" } },
        { description: { contains: search, mode: "insensitive" } },
        { admin: { name: { contains: search, mode: "insensitive" } } },
      ];
    }

    // Get audit logs for export (limit to 10000 records for performance)
    const auditLogs = await prisma.auditLog.findMany({
      where,
      include: {
        admin: {
          select: {
            name: true,
            email: true,
            role: true,
          },
        },
      },
      orderBy: { createdAt: "desc" },
      take: 10000, // Limit for performance
    });

    if (format === "csv") {
      const csvContent = generateCSV(auditLogs);

      return new NextResponse(csvContent, {
        headers: {
          "Content-Type": "text/csv",
          "Content-Disposition": `attachment; filename="audit-logs-${
            new Date().toISOString().split("T")[0]
          }.csv"`,
        },
      });
    } else {
      // For Excel format, we'll return JSON data that can be processed by frontend
      // Frontend can use libraries like xlsx to generate Excel files
      return NextResponse.json({
        data: auditLogs.map((log) => ({
          id: log.id,
          action: log.action,
          resource: log.resource,
          resourceId: log.resourceId || "",
          description: log.description || "",
          adminName: log.admin.name,
          adminEmail: log.admin.email,
          adminRole: log.admin.role,
          ipAddress: log.ipAddress || "",
          userAgent: log.userAgent || "",
          createdAt: log.createdAt.toLocaleString("vi-VN", {
            timeZone: "Asia/Ho_Chi_Minh",
          }),
          oldValues: log.oldValues ? JSON.stringify(log.oldValues) : "",
          newValues: log.newValues ? JSON.stringify(log.newValues) : "",
        })),
        filename: `audit-logs-${new Date().toISOString().split("T")[0]}.xlsx`,
      });
    }
  } catch (error) {
    console.error("Error exporting audit logs:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid export parameters", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

function generateCSV(auditLogs: any[]): string {
  const headers = [
    "ID",
    "Hành động",
    "Tài nguyên",
    "ID Tài nguyên",
    "Mô tả",
    "Tên Admin",
    "Email Admin",
    "Vai trò Admin",
    "Địa chỉ IP",
    "User Agent",
    "Thời gian",
    "Giá trị cũ",
    "Giá trị mới",
  ];

  const csvRows = [headers.join(",")];

  for (const log of auditLogs) {
    const row = [
      log.id,
      `"${log.action}"`,
      `"${log.resource}"`,
      `"${log.resourceId || ""}"`,
      `"${(log.description || "").replace(/"/g, '""')}"`,
      `"${log.admin.name}"`,
      `"${log.admin.email}"`,
      `"${log.admin.role}"`,
      `"${log.ipAddress || ""}"`,
      `"${(log.userAgent || "").replace(/"/g, '""')}"`,
      `"${log.createdAt.toLocaleString("vi-VN", {
        timeZone: "Asia/Ho_Chi_Minh",
      })}"`,
      `"${
        log.oldValues ? JSON.stringify(log.oldValues).replace(/"/g, '""') : ""
      }"`,
      `"${
        log.newValues ? JSON.stringify(log.newValues).replace(/"/g, '""') : ""
      }"`,
    ];
    csvRows.push(row.join(","));
  }

  return csvRows.join("\n");
}
