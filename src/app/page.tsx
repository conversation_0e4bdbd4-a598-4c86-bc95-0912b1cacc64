import { Metadata } from "next";
import { Header, Footer } from "@/components/layout";
import { HeroSection } from "@/components/shop/hero-section";
import { FeaturedProducts } from "@/components/shop/featured-products";
import { CategorySection } from "@/components/shop/category-section";
import { NewsletterSection } from "@/components/shop/newsletter-section";
import { TestimonialsSection } from "@/components/shop/testimonials-section";
import { TrendingProducts } from "@/components/shop/trending-products";
import { BrandsSection } from "@/components/shop/brands-section";
import { BlogSection } from "@/components/shop/blog-section";
import { StatsSection } from "@/components/shop/stats-section";
import { PromotionBanner } from "@/components/shop/promotion-banner";
import { WhyChooseUs } from "@/components/shop/why-choose-us";
import { InstagramFeed } from "@/components/shop/instagram-feed";

export const metadata: Metadata = {
  title: "NS Shop - Thời trang trực tuyến hàng đầ<PERSON>",
  description:
    "<PERSON>hám phá xu hướng thời trang mới nhất tại NS Shop. C<PERSON>a hàng thời trang trực tuyến với hơn 10,000+ sản phẩm chất lượng cao, giá cả hợp lý. Miễn phí vận chuyển toàn quốc.",
  keywords: [
    "thời trang",
    "quần áo",
    "giày dép",
    "phụ kiện",
    "mua sắm trực tuyến",
    "NS Shop",
  ],
  openGraph: {
    title: "NS Shop - Thời trang trực tuyến hàng đầu Việt Nam",
    description:
      "Khám phá xu hướng thời trang mới nhất tại NS Shop. Cửa hàng thời trang trực tuyến với hơn 10,000+ sản phẩm chất lượng cao.",
    images: ["/og-image-home.jpg"],
    url: "https://nsshop.com",
  },
  alternates: {
    canonical: "https://nsshop.com",
  },
};

export default function HomePage() {
  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-1">
        {/* Hero Section */}
        <HeroSection />

        {/* Promotion Banner */}
        <PromotionBanner />

        {/* Categories Section */}
        <CategorySection />

        {/* Featured Products */}
        <FeaturedProducts />

        {/* Stats Section */}
        <StatsSection />

        {/* Trending Products */}
        <TrendingProducts />

        {/* Why Choose Us */}
        <WhyChooseUs />

        {/* Testimonials */}
        <TestimonialsSection />

        {/* Brands Section */}
        <BrandsSection />

        {/* Instagram Feed */}
        <InstagramFeed />

        {/* Blog Section */}
        <BlogSection />

        {/* Newsletter */}
        <NewsletterSection />
      </main>
      <Footer />
    </div>
  );
}
