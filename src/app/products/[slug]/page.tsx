"use client";

import { useState, useEffect } from "react";
import { useParams } from "next/navigation";
import Image from "next/image";
import Link from "next/link";
import { <PERSON><PERSON>, <PERSON>er } from "@/components/layout";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { Dialog, DialogContent, DialogTrigger } from "@/components/ui/dialog";
import {
  Star,
  Heart,
  ShoppingCart,
  Minus,
  Plus,
  Share2,
  Truck,
  Shield,
  RotateCcw,
  ChevronLeft,
  ChevronRight,
  ZoomIn,
  GitCompare,
  Eye,
  Facebook,
  Twitter,
  Instagram,
  Copy,
  Check,
  ChevronDown,
  Info,
  Award,
  Users,
  TrendingUp,
} from "lucide-react";
import { toast } from "@/lib/toast";
import { useSession } from "next-auth/react";
import { formatCurrency } from "@/lib/utils";

interface ProductVariant {
  id: string;
  name: string;
  value: string;
  price?: number;
  stock?: number;
  image?: string;
}

interface ProductAttribute {
  id: string;
  name: string;
  values: ProductVariant[];
}

interface RelatedProduct {
  id: string;
  name: string;
  slug: string;
  price: number;
  salePrice?: number;
  image: string;
  avgRating: number;
  reviewCount: number;
}

interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  salePrice?: number;
  images: string[];
  stock: number;
  sku: string;
  tags: string[];
  avgRating: number;
  reviewCount: number;
  brand?: string;
  material?: string;
  weight?: string;
  dimensions?: string;
  isNew?: boolean;
  isBestseller?: boolean;
  isTrending?: boolean;
  viewCount?: number;
  soldCount?: number;
  category: {
    id: string;
    name: string;
    slug: string;
  };
  attributes?: ProductAttribute[];
  relatedProducts?: RelatedProduct[];
  specifications?: { [key: string]: string };
  reviews: Array<{
    id: string;
    rating: number;
    comment?: string;
    images: string[];
    createdAt: string;
    helpful: number;
    user: {
      id: string;
      name: string;
      avatar?: string;
      verified?: boolean;
    };
  }>;
}

export default function ProductDetailPage() {
  const params = useParams();
  const { data: session } = useSession();
  const [product, setProduct] = useState<Product | null>(null);
  const [loading, setLoading] = useState(true);
  const [quantity, setQuantity] = useState(1);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [isWishlisted, setIsWishlisted] = useState(false);
  const [selectedVariants, setSelectedVariants] = useState<{
    [key: string]: string;
  }>({});
  const [isImageZoomed, setIsImageZoomed] = useState(false);
  const [activeTab, setActiveTab] = useState("description");
  const [copiedLink, setCopiedLink] = useState(false);
  const [compareList, setCompareList] = useState<string[]>([]);
  const [recentlyViewed, setRecentlyViewed] = useState<RelatedProduct[]>([]);

  useEffect(() => {
    const fetchProduct = async () => {
      try {
        console.log("Fetching product with slug:", params.slug);
        const response = await fetch(`/api/products/${params.slug}`, {
          cache: "no-store",
          headers: {
            "Content-Type": "application/json",
          },
        });

        console.log("Product API response status:", response.status);
        const data = await response.json();
        console.log("Product API response data:", data);

        if (response.ok) {
          setProduct(data);
        } else {
          toast.error(data.error || "Không tìm thấy sản phẩm");
        }
      } catch (error) {
        console.error("Error fetching product:", error);
        toast.error("Có lỗi xảy ra khi tải sản phẩm");
      } finally {
        setLoading(false);
      }
    };

    if (params.slug) {
      fetchProduct();
    }
  }, [params.slug]);

  // Helper functions
  const addToRecentlyViewed = (product: Product) => {
    const productData: RelatedProduct = {
      id: product.id,
      name: product.name,
      slug: params.slug as string,
      price: product.price,
      salePrice: product.salePrice,
      image: product.images[0],
      avgRating: product.avgRating,
      reviewCount: product.reviewCount,
    };

    setRecentlyViewed((prev) => {
      const filtered = prev.filter((p) => p.id !== product.id);
      return [productData, ...filtered].slice(0, 5);
    });
  };

  const toggleCompare = () => {
    if (!product) return;

    if (compareList.includes(product.id)) {
      setCompareList((prev) => prev.filter((id) => id !== product.id));
      toast.success("Đã xóa khỏi danh sách so sánh");
    } else if (compareList.length < 3) {
      setCompareList((prev) => [...prev, product.id]);
      toast.success("Đã thêm vào danh sách so sánh");
    } else {
      toast.error("Chỉ có thể so sánh tối đa 3 sản phẩm");
    }
  };

  const copyProductLink = async () => {
    try {
      await navigator.clipboard.writeText(window.location.href);
      setCopiedLink(true);
      toast.success("Đã sao chép link sản phẩm");
      setTimeout(() => setCopiedLink(false), 2000);
    } catch (error) {
      toast.error("Không thể sao chép link");
    }
  };

  const shareToSocial = (platform: string) => {
    const url = encodeURIComponent(window.location.href);
    const text = encodeURIComponent(
      `Xem sản phẩm ${product?.name} tại NS Shop`
    );

    let shareUrl = "";
    switch (platform) {
      case "facebook":
        shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${url}`;
        break;
      case "twitter":
        shareUrl = `https://twitter.com/intent/tweet?url=${url}&text=${text}`;
        break;
      case "instagram":
        // Instagram doesn't support direct URL sharing, so copy to clipboard
        copyProductLink();
        return;
    }

    if (shareUrl) {
      window.open(shareUrl, "_blank", "width=600,height=400");
    }
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 ${
          i < Math.floor(rating)
            ? "text-yellow-400 fill-current"
            : "text-gray-300"
        }`}
      />
    ));
  };

  const handleAddToCart = async () => {
    if (!session) {
      toast.error("Vui lòng đăng nhập để thêm vào giỏ hàng");
      return;
    }

    if (!product) return;

    try {
      const response = await fetch("/api/cart", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          productId: product.id,
          quantity,
        }),
      });

      const data = await response.json();

      if (response.ok) {
        toast.success("Đã thêm vào giỏ hàng");
      } else {
        toast.error(data.error || "Có lỗi xảy ra");
      }
    } catch (error) {
      toast.error("Có lỗi xảy ra khi thêm vào giỏ hàng");
    }
  };

  const handleWishlist = () => {
    if (!session) {
      toast.error("Vui lòng đăng nhập để thêm vào danh sách yêu thích");
      return;
    }

    setIsWishlisted(!isWishlisted);
    toast.success(
      isWishlisted
        ? "Đã xóa khỏi danh sách yêu thích"
        : "Đã thêm vào danh sách yêu thích"
    );
  };

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: product?.name,
          text: product?.description,
          url: window.location.href,
        });
      } catch (error) {
        // User cancelled sharing
      }
    } else {
      // Fallback to copying URL
      navigator.clipboard.writeText(window.location.href);
      toast.success("Đã sao chép link sản phẩm");
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex flex-col">
        <Header />
        <main className="flex-1 container mx-auto px-4 py-8">
          <div className="animate-pulse">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <div className="space-y-4">
                <div className="aspect-square bg-gray-200 rounded-lg" />
                <div className="grid grid-cols-4 gap-2">
                  {Array.from({ length: 4 }, (_, i) => (
                    <div
                      key={i}
                      className="aspect-square bg-gray-200 rounded"
                    />
                  ))}
                </div>
              </div>
              <div className="space-y-4">
                <div className="h-8 bg-gray-200 rounded" />
                <div className="h-4 bg-gray-200 rounded w-2/3" />
                <div className="h-6 bg-gray-200 rounded w-1/2" />
                <div className="h-20 bg-gray-200 rounded" />
              </div>
            </div>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  if (!product) {
    return (
      <div className="min-h-screen flex flex-col">
        <Header />
        <main className="flex-1 container mx-auto px-4 py-8">
          <div className="text-center py-12">
            <h1 className="text-2xl font-bold mb-4">Không tìm thấy sản phẩm</h1>
            <Link href="/products">
              <Button>Quay lại danh sách sản phẩm</Button>
            </Link>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col">
      <Header />

      <main className="flex-1 container mx-auto px-4 py-8">
        {/* Breadcrumb */}
        <nav className="flex items-center space-x-2 text-sm text-muted-foreground mb-8">
          <Link href="/" className="hover:text-foreground">
            Trang chủ
          </Link>
          <span>/</span>
          <Link href="/products" className="hover:text-foreground">
            Sản phẩm
          </Link>
          <span>/</span>
          <Link
            href={`/categories/${product.category.slug}`}
            className="hover:text-foreground"
          >
            {product.category.name}
          </Link>
          <span>/</span>
          <span className="text-foreground">{product.name}</span>
        </nav>

        {/* Product Details */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
          {/* Enhanced Product Images */}
          <div className="space-y-4">
            {/* Main Image with Zoom */}
            <div className="relative aspect-square overflow-hidden rounded-xl bg-gradient-to-br from-muted to-muted-foreground/10 group">
              <Dialog open={isImageZoomed} onOpenChange={setIsImageZoomed}>
                <DialogTrigger asChild>
                  <div className="relative w-full h-full cursor-zoom-in">
                    <Image
                      src={
                        product.images[selectedImageIndex] ||
                        "/images/placeholder.jpg"
                      }
                      alt={product.name}
                      fill
                      className="object-cover transition-transform duration-300 group-hover:scale-105"
                    />

                    {/* Badges */}
                    <div className="absolute top-4 left-4 flex flex-col gap-2">
                      {product.salePrice && (
                        <Badge className="bg-red-500 text-white">
                          -
                          {Math.round(
                            ((product.price - product.salePrice) /
                              product.price) *
                              100
                          )}
                          %
                        </Badge>
                      )}
                      {product.isNew && (
                        <Badge className="bg-green-500 text-white">Mới</Badge>
                      )}
                      {product.isBestseller && (
                        <Badge className="bg-orange-500 text-white">
                          Bestseller
                        </Badge>
                      )}
                      {product.isTrending && (
                        <Badge className="bg-fashion-500 text-white">
                          <TrendingUp className="h-3 w-3 mr-1" />
                          Trending
                        </Badge>
                      )}
                    </div>

                    {/* Zoom Icon */}
                    <div className="absolute top-4 right-4 bg-white/80 backdrop-blur-sm rounded-full p-2 opacity-0 group-hover:opacity-100 transition-opacity">
                      <ZoomIn className="h-4 w-4" />
                    </div>

                    {/* Navigation Arrows */}
                    {product.images.length > 1 && (
                      <>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            setSelectedImageIndex(
                              selectedImageIndex === 0
                                ? product.images.length - 1
                                : selectedImageIndex - 1
                            );
                          }}
                          className="absolute left-3 top-1/2 transform -translate-y-1/2 bg-white/90 hover:bg-white rounded-full p-2 transition-all opacity-0 group-hover:opacity-100 shadow-lg"
                        >
                          <ChevronLeft className="h-4 w-4" />
                        </button>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            setSelectedImageIndex(
                              selectedImageIndex === product.images.length - 1
                                ? 0
                                : selectedImageIndex + 1
                            );
                          }}
                          className="absolute right-3 top-1/2 transform -translate-y-1/2 bg-white/90 hover:bg-white rounded-full p-2 transition-all opacity-0 group-hover:opacity-100 shadow-lg"
                        >
                          <ChevronRight className="h-4 w-4" />
                        </button>
                      </>
                    )}

                    {/* Image Counter */}
                    {product.images.length > 1 && (
                      <div className="absolute bottom-4 right-4 bg-black/60 text-white px-2 py-1 rounded text-xs">
                        {selectedImageIndex + 1} / {product.images.length}
                      </div>
                    )}
                  </div>
                </DialogTrigger>
                <DialogContent className="max-w-4xl max-h-[90vh] p-0">
                  <div className="relative aspect-square">
                    <Image
                      src={
                        product.images[selectedImageIndex] ||
                        "/images/placeholder.jpg"
                      }
                      alt={product.name}
                      fill
                      className="object-contain"
                    />
                  </div>
                </DialogContent>
              </Dialog>
            </div>

            {/* Enhanced Thumbnail Images */}
            {product.images.length > 1 && (
              <div className="grid grid-cols-5 gap-2">
                {product.images.map((image, index) => (
                  <button
                    key={index}
                    onClick={() => setSelectedImageIndex(index)}
                    className={`relative aspect-square overflow-hidden rounded-lg border-2 transition-all duration-200 ${
                      selectedImageIndex === index
                        ? "border-fashion-500 ring-2 ring-fashion-200 scale-105"
                        : "border-gray-200 hover:border-gray-300 hover:scale-102"
                    }`}
                  >
                    <Image
                      src={image}
                      alt={`${product.name} ${index + 1}`}
                      fill
                      className="object-cover"
                    />
                  </button>
                ))}
              </div>
            )}

            {/* Product Stats */}
            <div className="grid grid-cols-3 gap-4 p-4 bg-muted/30 rounded-lg">
              <div className="text-center">
                <div className="flex items-center justify-center mb-1">
                  <Eye className="h-4 w-4 text-muted-foreground mr-1" />
                </div>
                <div className="text-sm font-medium">
                  {product.viewCount || 0}
                </div>
                <div className="text-xs text-muted-foreground">Lượt xem</div>
              </div>
              <div className="text-center">
                <div className="flex items-center justify-center mb-1">
                  <ShoppingCart className="h-4 w-4 text-muted-foreground mr-1" />
                </div>
                <div className="text-sm font-medium">
                  {product.soldCount || 0}
                </div>
                <div className="text-xs text-muted-foreground">Đã bán</div>
              </div>
              <div className="text-center">
                <div className="flex items-center justify-center mb-1">
                  <Users className="h-4 w-4 text-muted-foreground mr-1" />
                </div>
                <div className="text-sm font-medium">{product.reviewCount}</div>
                <div className="text-xs text-muted-foreground">Đánh giá</div>
              </div>
            </div>
          </div>

          {/* Enhanced Product Info */}
          <div className="space-y-6">
            {/* Title, Brand and Rating */}
            <div>
              <div className="flex items-center gap-2 mb-2">
                {product.brand && (
                  <Badge variant="outline" className="text-xs">
                    {product.brand}
                  </Badge>
                )}
                <Badge variant="outline" className="text-xs">
                  {product.category.name}
                </Badge>
              </div>

              <h1 className="text-3xl lg:text-4xl font-bold mb-3 leading-tight">
                {product.name}
              </h1>

              <div className="flex items-center gap-4 mb-3">
                <div className="flex items-center gap-2">
                  <div className="flex items-center gap-1">
                    {renderStars(product.avgRating)}
                  </div>
                  <span className="text-sm font-medium">
                    {product.avgRating.toFixed(1)}
                  </span>
                  <span className="text-sm text-muted-foreground">
                    ({product.reviewCount} đánh giá)
                  </span>
                </div>

                {product.avgRating >= 4.5 && (
                  <Badge className="bg-yellow-500 text-white">
                    <Award className="h-3 w-3 mr-1" />
                    Chất lượng cao
                  </Badge>
                )}
              </div>

              <div className="flex items-center gap-4 text-sm text-muted-foreground">
                <span>SKU: {product.sku}</span>
                {product.stock > 0 ? (
                  <span className="text-green-600 font-medium">
                    ✓ Còn hàng ({product.stock} sản phẩm)
                  </span>
                ) : (
                  <span className="text-red-600 font-medium">✗ Hết hàng</span>
                )}
              </div>
            </div>

            {/* Enhanced Price */}
            <div className="space-y-3 p-4 bg-gradient-to-r from-fashion-50 to-fashion-100 dark:from-fashion-900/20 dark:to-fashion-800/20 rounded-xl">
              {product.salePrice ? (
                <>
                  <div className="flex items-center gap-3">
                    <div className="text-3xl lg:text-4xl font-bold text-fashion-600">
                      {formatCurrency(product.salePrice)}
                    </div>
                    <div className="text-lg text-muted-foreground line-through">
                      {formatCurrency(product.price)}
                    </div>
                  </div>
                  <div className="text-sm text-green-600 font-medium">
                    Tiết kiệm{" "}
                    {formatCurrency(product.price - product.salePrice)}(
                    {Math.round(
                      ((product.price - product.salePrice) / product.price) *
                        100
                    )}
                    %)
                  </div>
                </>
              ) : (
                <div className="text-3xl lg:text-4xl font-bold text-fashion-600">
                  {formatCurrency(product.price)}
                </div>
              )}
            </div>

            {/* Quick Description */}
            <div className="prose prose-sm max-w-none">
              <p className="text-muted-foreground leading-relaxed">
                {product.description}
              </p>
            </div>

            {/* Description */}
            <div>
              <h3 className="font-semibold mb-2">Mô tả sản phẩm</h3>
              <p className="text-muted-foreground leading-relaxed">
                {product.description}
              </p>
            </div>

            {/* Tags */}
            {product.tags.length > 0 && (
              <div>
                <h3 className="font-semibold mb-2">Tags</h3>
                <div className="flex flex-wrap gap-2">
                  {product.tags.map((tag, index) => (
                    <span
                      key={index}
                      className="px-2 py-1 bg-gray-100 text-sm rounded"
                    >
                      {tag}
                    </span>
                  ))}
                </div>
              </div>
            )}

            {/* Enhanced Quantity and Actions */}
            <div className="space-y-6">
              {/* Quantity Selector */}
              <div>
                <label className="block text-sm font-medium mb-3">
                  Số lượng
                </label>
                <div className="flex items-center gap-3">
                  <div className="flex items-center border rounded-lg">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setQuantity(Math.max(1, quantity - 1))}
                      disabled={quantity <= 1}
                      className="h-10 w-10 p-0 rounded-r-none"
                    >
                      <Minus className="h-4 w-4" />
                    </Button>
                    <div className="w-16 h-10 flex items-center justify-center border-x font-medium">
                      {quantity}
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() =>
                        setQuantity(Math.min(product.stock, quantity + 1))
                      }
                      disabled={quantity >= product.stock}
                      className="h-10 w-10 p-0 rounded-l-none"
                    >
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>
                  <span className="text-sm text-muted-foreground">
                    Tối đa {product.stock} sản phẩm
                  </span>
                </div>
              </div>

              {/* Main Action Buttons */}
              <div className="space-y-3">
                <div className="flex gap-3">
                  <Button
                    onClick={handleAddToCart}
                    className="flex-1 h-12 bg-fashion-600 hover:bg-fashion-700 text-white font-medium"
                    disabled={product.stock === 0}
                    size="lg"
                  >
                    <ShoppingCart className="h-5 w-5 mr-2" />
                    {product.stock === 0 ? "Hết hàng" : "Thêm vào giỏ hàng"}
                  </Button>
                  <Button
                    variant="outline"
                    onClick={handleWishlist}
                    className={`h-12 px-4 ${
                      isWishlisted
                        ? "text-fashion-600 border-fashion-600 bg-fashion-50"
                        : ""
                    }`}
                    size="lg"
                  >
                    <Heart
                      className={`h-5 w-5 ${
                        isWishlisted ? "fill-current" : ""
                      }`}
                    />
                  </Button>
                </div>

                {/* Secondary Actions */}
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    onClick={toggleCompare}
                    className="flex-1"
                    size="sm"
                  >
                    <GitCompare className="h-4 w-4 mr-2" />
                    {compareList.includes(product.id)
                      ? "Đã thêm so sánh"
                      : "So sánh"}
                  </Button>
                  <Button
                    variant="outline"
                    onClick={copyProductLink}
                    size="sm"
                    className="px-3"
                  >
                    {copiedLink ? (
                      <Check className="h-4 w-4 text-green-600" />
                    ) : (
                      <Copy className="h-4 w-4" />
                    )}
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => shareToSocial("facebook")}
                    size="sm"
                    className="px-3"
                  >
                    <Facebook className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => shareToSocial("twitter")}
                    size="sm"
                    className="px-3"
                  >
                    <Twitter className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>

            {/* Features */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 pt-6 border-t">
              <div className="flex items-center gap-2 text-sm">
                <Truck className="h-4 w-4 text-green-600" />
                <span>Miễn phí vận chuyển</span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <Shield className="h-4 w-4 text-blue-600" />
                <span>Bảo hành chính hãng</span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <RotateCcw className="h-4 w-4 text-orange-600" />
                <span>Đổi trả 30 ngày</span>
              </div>
            </div>
          </div>
        </div>

        {/* Reviews Section */}
        <div className="space-y-6">
          <h2 className="text-2xl font-bold">Đánh giá sản phẩm</h2>

          {product.reviews.length === 0 ? (
            <Card>
              <CardContent className="p-6 text-center">
                <p className="text-muted-foreground">
                  Chưa có đánh giá nào cho sản phẩm này
                </p>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {product.reviews.map((review) => (
                <Card key={review.id}>
                  <CardContent className="p-6">
                    <div className="flex items-start gap-4">
                      <div className="w-10 h-10 bg-pink-100 rounded-full flex items-center justify-center">
                        {review.user.avatar ? (
                          <Image
                            src={review.user.avatar}
                            alt={review.user.name}
                            width={40}
                            height={40}
                            className="rounded-full"
                          />
                        ) : (
                          <span className="text-pink-600 font-medium">
                            {review.user.name.charAt(0).toUpperCase()}
                          </span>
                        )}
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <span className="font-medium">
                            {review.user.name}
                          </span>
                          <div className="flex items-center gap-1">
                            {renderStars(review.rating)}
                          </div>
                          <span className="text-sm text-muted-foreground">
                            {new Date(review.createdAt).toLocaleDateString(
                              "vi-VN"
                            )}
                          </span>
                        </div>
                        {review.comment && (
                          <p className="text-muted-foreground mb-2">
                            {review.comment}
                          </p>
                        )}
                        {review.images.length > 0 && (
                          <div className="flex gap-2">
                            {review.images.map((image, index) => (
                              <div
                                key={index}
                                className="relative w-16 h-16 rounded overflow-hidden"
                              >
                                <Image
                                  src={image}
                                  alt={`Review image ${index + 1}`}
                                  fill
                                  className="object-cover"
                                />
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </div>
      </main>

      <Footer />
    </div>
  );
}
